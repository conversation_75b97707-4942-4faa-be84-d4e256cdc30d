<template>
    <div class="courses-page">
        <van-nav-bar title="课程中心" fixed placeholder />

        <div class="content">
            <!-- 搜索栏 -->
            <div class="search-section">
                <van-search
                    v-model="searchValue"
                    placeholder="搜索课程"
                    @search="onSearch"
                    @clear="onClear"
                />
            </div>

            <!-- 分类标签 -->
            <div class="category-section">
                <van-tabs v-model="activeCategory" @change="onCategoryChange" :ellipsis="false">
                    <van-tab
                        v-for="category in categories"
                        :key="category.ID"
                        :title="category.CategoryName"
                        :name="category.ID"
                    />
                </van-tabs>
            </div>

            <!-- 课程列表 -->
            <div class="course-list">
                <!-- 空状态 -->
                <van-empty
                    v-if="!loading && courseList.length === 0 && finished"
                    description="暂无课程数据"
                    image="search"
                />

                <van-list
                    v-else
                    v-model="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    loading-text="加载中..."
                    error-text="请求失败，点击重新加载"
                    @load="onLoad"
                >
                    <div class="course-item">
                        <div
                            v-for="course in courseList"
                            :key="course.ID"
                            class="course-card"
                            @click="goToCourseDetail(course)"
                        >
                            <div class="course-image">
                                <van-image
                                    :src="course.CoverImage || course.ListImage"
                                    fit="cover"
                                />
                                <div class="course-tags">
                                    <van-tag v-if="course.IsHot" type="warning" size="mini"
                                        >热门</van-tag
                                    >
                                    <van-tag :type="getLevelColor(course.Level)" size="mini">{{
                                        getLevelText(course.Level)
                                    }}</van-tag>
                                </div>
                                <div class="course-duration">
                                    <van-icon name="clock-o" />
                                    {{ formatDuration(course.TotalDuration) }}
                                </div>
                            </div>
                            <div class="course-info">
                                <div class="course-title">{{ course.CourseName }}</div>
                                <div class="course-desc">{{ course.CourseDesc }}</div>
                                <div class="course-teacher" v-if="course.DocentName">
                                    <van-icon name="manager-o" />
                                    {{ course.DocentName }}
                                </div>
                                <div class="course-meta">
                                    <div class="course-lessons">
                                        <van-icon name="play-circle-o" />
                                        {{ course.TotalLessons }}个课时
                                    </div>
                                    <div
                                        class="course-requirements"
                                        v-if="course.FinishRequirements"
                                    >
                                        <van-icon name="certificate" />
                                        {{ course.FinishRequirements }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </van-list>
            </div>
        </div>
    </div>
</template>

<script>
import { getCourseCategories, getCourseList } from '@/api/course'

export default {
    name: 'CoursesPage',
    data() {
        return {
            searchValue: '',
            activeCategory: 0,
            loading: false,
            finished: false,
            categories: [],
            courseList: [],
            selectedCategory: { ID: 0, CategoryName: '全部', CategoryID: 'ALL' },
            currentPage: 1,
            pageSize: 5,
            hasError: false,
            isFirstLoad: true
        }
    },
    created() {
        console.log('页面创建，开始加载分类')
        this.loadCourseCategories()
    },
    mounted() {
        console.log('页面挂载完成', {
            loading: this.loading,
            finished: this.finished,
            courseList: this.courseList.length
        })
    },
    methods: {
        formatDuration(seconds) {
            if (!seconds) return '0秒'
            const hours = Math.floor(seconds / 3600)
            const minutes = Math.floor((seconds % 3600) / 60)
            const secs = Math.floor(seconds % 60)

            let result = ''
            if (hours > 0) {
                result += `${hours}小时`
            }
            if (minutes > 0) {
                result += `${minutes}分钟`
            }
            if (secs > 0 || result === '') {
                result += `${secs}秒`
            }

            return result
        },
        getLevelText(level) {
            const levelMap = {
                1: '初级',
                2: '中级',
                3: '高级',
                4: '专家级'
            }
            return levelMap[level] || '初级'
        },
        getLevelColor(level) {
            const colorMap = {
                1: 'success', // 绿色
                2: 'primary', // 蓝色
                3: 'danger', // 红色
                4: 'warning' // 橙色（专家级）
            }
            return colorMap[level] || 'success'
        },
        // 获取课程分类
        async loadCourseCategories() {
            try {
                const res = await getCourseCategories()
                console.log('课程分类:', res)

                if (res && res.Data && res.Data.PageList) {
                    // 在分类列表前添加"全部"选项
                    this.categories = [
                        { ID: 0, CategoryName: '全部', CategoryID: 'ALL' },
                        ...res.Data.PageList
                    ]
                } else if (res && res.Data) {
                    // 如果直接返回数组
                    const categoryList = Array.isArray(res.Data) ? res.Data : []
                    this.categories = [
                        { ID: 0, CategoryName: '全部', CategoryID: 'ALL' },
                        ...categoryList
                    ]
                } else {
                    // 如果没有分类数据，至少添加"全部"选项
                    this.categories = [{ ID: 0, CategoryName: '全部', CategoryID: 'ALL' }]
                }
            } catch (error) {
                console.error('获取课程分类失败:', error)
                // 即使分类获取失败，也要设置默认分类
                this.categories = [{ ID: 0, CategoryName: '全部', CategoryID: 'ALL' }]
            } finally {
                // 无论分类是否获取成功，都要加载课程列表
                console.log('分类加载完成，准备加载课程列表')
                this.onLoad()
            }
        },
        // 获取课程列表
        async loadCourseList() {
            try {
                this.hasError = false

                const requestParams = {
                    PageIndex: this.currentPage,
                    PageSize: this.pageSize,
                    // 搜索框关键字
                    CourseName: this.searchValue || undefined,
                    CategoryID:
                        this.selectedCategory.CategoryID === 'ALL'
                            ? undefined
                            : this.selectedCategory.CategoryID
                }

                console.log('发送课程列表请求:', requestParams)
                const res = await getCourseList(requestParams)
                console.log('课程列表响应:', res)

                if (res && res.Data && res.Data.PageList) {
                    const newList = res.Data.PageList || []

                    // 如果是第一页，替换列表；否则追加到列表
                    if (this.currentPage === 1) {
                        this.courseList = newList
                    } else {
                        this.courseList = [...this.courseList, ...newList]
                    }

                    // 判断是否还有更多数据
                    this.finished = newList.length < this.pageSize

                    // 如果有数据且未完成，准备下一页
                    if (newList.length > 0 && !this.finished) {
                        this.currentPage++
                    }
                } else if (res && res.Data) {
                    const list = Array.isArray(res.Data) ? res.Data : []

                    if (this.currentPage === 1) {
                        this.courseList = list
                    } else {
                        this.courseList = [...this.courseList, ...list]
                    }

                    this.finished = list.length < this.pageSize

                    // 如果有数据且未完成，准备下一页
                    if (list.length > 0 && !this.finished) {
                        this.currentPage++
                    }
                } else {
                    // 没有数据时标记为完成
                    this.finished = true
                }

                this.isFirstLoad = false
            } catch (error) {
                console.error('获取课程列表失败:', error)
                this.hasError = true
                this.finished = true

                // 显示错误提示
                this.$toast('加载失败，请稍后重试')
            }
        },
        // 重置分页状态
        resetPagination() {
            this.currentPage = 1
            this.finished = false
            this.loading = false
            this.courseList = []
            this.hasError = false
            this.isFirstLoad = true
        },
        onSearch(value) {
            console.log('搜索:', value)
            // 重置分页参数并重新加载
            this.resetPagination()
            this.onLoad()
        },
        onClear() {
            this.searchValue = ''
            // 清空搜索结果，重新加载
            this.resetPagination()
            this.onLoad()
        },
        onCategoryChange(name) {
            // 找到选中的分类
            this.selectedCategory = this.categories.find(cat => cat.ID === name) || {
                ID: 0,
                CategoryName: '全部',
                CategoryID: 'ALL'
            }

            console.log('分类切换:', this.selectedCategory.CategoryName)

            // 重置分页并加载对应分类的课程
            this.resetPagination()
            this.onLoad()
        },
        onLoad() {
            console.log('onLoad 被调用', {
                loading: this.loading,
                finished: this.finished,
                isFirstLoad: this.isFirstLoad,
                currentPage: this.currentPage
            })

            // 如果已经在加载中或已经加载完成，则不再加载
            if (this.loading || this.finished) {
                console.log('跳过加载：', { loading: this.loading, finished: this.finished })
                return
            }

            console.log('开始加载课程列表，页码：', this.currentPage)
            this.loading = true
            this.loadCourseList().finally(() => {
                this.loading = false
            })
        },
        goToCourseDetail(course) {
            console.log('进入课程详情:', course)
            // 跳转到课程详情页，使用课程ID作为参数
            this.$router.push(`/course-detail/${course.ID}`)
        }
    }
}
</script>

<style lang="scss" scoped>
// 蓝白主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$lighter-blue: #60a5fa;
$lightest-blue: #dbeafe;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.courses-page {
    background: #f5f6f7;
    min-height: 100vh;
    overflow-x: hidden;

    // 隐藏滚动条
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }

    .content {
        padding: 20px 16px;

        .search-section {
            margin-bottom: 20px;

            ::v-deep .van-search {
                .van-search__content {
                    border: 1px solid #2563eb !important;
                    border-radius: 10px !important;
                }

                input {
                    color: $text-primary;
                    font-weight: 500;

                    &::placeholder {
                        color: $text-secondary;
                    }
                }
            }
        }

        .category-section {
            margin-bottom: 24px;
            background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(37, 99, 235, 0.08);
            border: 1px solid rgba(37, 99, 235, 0.05);
            overflow: hidden;

            ::v-deep .van-tabs {
                .van-tabs__wrap {
                    background: transparent;
                }

                .van-tabs__nav {
                    background: transparent;
                }

                .van-tab {
                    color: $text-secondary;
                    font-weight: 600;

                    &--active {
                        color: $primary-blue;
                    }
                }

                .van-tabs__line {
                    background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                    height: 3px;
                    border-radius: 2px;
                }
            }
        }

        .course-list {
            // 空状态样式
            ::v-deep .van-empty {
                padding: 60px 20px;

                .van-empty__image {
                    width: 120px;
                    height: 120px;
                }

                .van-empty__description {
                    color: $text-secondary;
                    font-size: 14px;
                    margin-top: 16px;
                }
            }

            .course-item {
                display: grid;
                grid-template-columns: repeat(1, 1fr);
                gap: 16px;

                .course-card {
                    background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                    border-radius: 16px;
                    overflow: hidden;
                    box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
                    border: 1px solid rgba(37, 99, 235, 0.05);
                    transition: all 0.3s ease;

                    &:active {
                        transform: scale(0.96);
                        box-shadow: 0 8px 32px rgba(37, 99, 235, 0.15);
                    }

                    .course-image {
                        position: relative;
                        height: 200px;

                        ::v-deep .van-image {
                            width: 100%;
                            height: 100%;
                        }

                        .course-tags {
                            position: absolute;
                            top: 12px;
                            left: 12px;
                            display: flex;
                            gap: 8px;

                            ::v-deep .van-tag {
                                font-weight: 600;
                                border-radius: 8px;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                                backdrop-filter: blur(4px);
                            }
                        }

                        .course-duration {
                            position: absolute;
                            bottom: 12px;
                            right: 12px;
                            background: rgba(0, 0, 0, 0.7);
                            color: white;
                            padding: 6px 10px;
                            border-radius: 12px;
                            font-size: 12px;
                            font-weight: 600;
                            display: flex;
                            align-items: center;
                            gap: 4px;
                            backdrop-filter: blur(4px);

                            .van-icon {
                                font-size: 12px;
                            }
                        }
                    }

                    .course-info {
                        padding: 18px;

                        .course-title {
                            font-size: 16px;
                            font-weight: 700;
                            color: $text-primary;
                            margin-bottom: 8px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            line-height: 1.4;
                        }

                        .course-desc {
                            font-size: 13px;
                            color: $text-secondary;
                            margin-bottom: 12px;
                            line-height: 1.5;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            line-clamp: 2;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .course-teacher {
                            font-size: 13px;
                            color: $primary-blue;
                            margin-bottom: 12px;
                            font-weight: 600;
                            display: flex;
                            align-items: center;
                            gap: 6px;

                            .van-icon {
                                font-size: 14px;
                            }
                        }

                        .course-meta {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            gap: 12px;

                            .course-lessons {
                                font-size: 12px;
                                color: $text-secondary;
                                font-weight: 600;
                                display: flex;
                                align-items: center;
                                gap: 4px;
                                background: rgba(37, 99, 235, 0.08);
                                padding: 4px 8px;
                                border-radius: 8px;

                                .van-icon {
                                    font-size: 12px;
                                    color: $primary-blue;
                                }
                            }

                            .course-requirements {
                                font-size: 11px;
                                color: $text-light;
                                font-weight: 500;
                                display: flex;
                                align-items: center;
                                gap: 4px;

                                .van-icon {
                                    font-size: 11px;
                                    color: #10b981;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
